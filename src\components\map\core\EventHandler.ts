import mapboxgl from "mapbox-gl";
import type { DaycareFeature } from "@/types/daycare";
import { MapManager } from "./MapManager";

/**
 * 事件处理器
 * 负责处理地图点击、悬停等交互事件
 */
export class EventHandler {
  private mapManager: MapManager;
  private popupRef: mapboxgl.Popup | null = null;
  private onPopupCreate?: (
    popup: mapboxgl.Popup,
    feature: DaycareFeature
  ) => void;

  constructor(mapManager: MapManager) {
    this.mapManager = mapManager;
  }

  /**
   * 设置弹窗创建回调
   */
  setPopupCreateCallback(
    callback: (popup: mapboxgl.Popup, feature: DaycareFeature) => void
  ): void {
    this.onPopupCreate = callback;
  }

  /**
   * 初始化ZIP区域事件
   */
  initializeZipAreaEvents(): void {
    // 添加点击事件
    this.mapManager.onClick(
      "daycare-zip-fill",
      this.handleZipAreaClick.bind(this)
    );

    // 添加鼠标悬停效果
    this.mapManager.onMouseEnter("daycare-zip-fill", () => {
      this.mapManager.setCursor("pointer");
    });

    this.mapManager.onMouseLeave("daycare-zip-fill", () => {
      this.mapManager.setCursor("");
    });
  }

  /**
   * 初始化圆点事件
   */
  initializeCircleEvents(): void {
    // 添加点击事件
    this.mapManager.onClick(
      "daycare-circles",
      this.handleCircleClick.bind(this)
    );

    // 添加鼠标悬停效果
    this.mapManager.onMouseEnter("daycare-circles", () => {
      this.mapManager.setCursor("pointer");
    });

    this.mapManager.onMouseLeave("daycare-circles", () => {
      this.mapManager.setCursor("");
    });
  }

  /**
   * 处理ZIP区域点击事件
   */
  private handleZipAreaClick(
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ): void {
    if (!e.features || e.features.length === 0) {
      return;
    }

    const feature = e.features[0] as any as DaycareFeature;
    this.createPopup(e.lngLat, feature);
  }

  /**
   * 处理圆点点击事件
   */
  private handleCircleClick(
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ): void {
    if (!e.features || e.features.length === 0) {
      return;
    }

    const feature = e.features[0] as any as DaycareFeature;
    this.createPopup(e.lngLat, feature);
  }

  /**
   * 创建弹窗
   */
  private createPopup(lngLat: mapboxgl.LngLat, feature: DaycareFeature): void {
    const map = this.mapManager.getMap();
    if (!map) return;

    // 关闭现有弹窗
    if (this.popupRef) {
      this.popupRef.remove();
    }

    // 创建新弹窗
    const popup = new mapboxgl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: true,
    }).setLngLat(lngLat);

    // 使用 setTimeout 确保弹窗在正确位置显示
    setTimeout(() => {
      popup.addTo(map);
      this.popupRef = popup;

      // 调用弹窗创建回调
      if (this.onPopupCreate) {
        this.onPopupCreate(popup, feature);
      }
    }, 0);

    // 弹窗关闭事件
    popup.on("close", () => {
      this.popupRef = null;
    });
  }

  /**
   * 初始化飞行到位置事件监听
   */
  initializeFlyToEvents(): void {
    const handleMapFlyTo = (event: CustomEvent) => {
      const { coordinates, zoom } = event.detail;
      this.mapManager.flyTo(coordinates, zoom);
    };

    window.addEventListener("mapFlyTo", handleMapFlyTo as EventListener);

    // 返回清理函数
    return () => {
      window.removeEventListener("mapFlyTo", handleMapFlyTo as EventListener);
    };
  }

  /**
   * 关闭当前弹窗
   */
  closePopup(): void {
    if (this.popupRef) {
      this.popupRef.remove();
      this.popupRef = null;
    }
  }

  /**
   * 获取当前弹窗
   */
  getCurrentPopup(): mapboxgl.Popup | null {
    return this.popupRef;
  }

  /**
   * 清理事件监听器
   */
  cleanup(): void {
    this.closePopup();
  }
}
